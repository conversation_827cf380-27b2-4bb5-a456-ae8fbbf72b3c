// /*-- result --*/
.result
  $root: &
  margin-top: 75px
  display: none
  &.active
    display: block
  .form__body
    margin-top: 55px
    @media (max-width:$bk-tb)
      margin-top: 30px
  &__wrap
    margin-top: 30px
    &:first-child
      margin-top: 0
    &-item
      position: relative
  &__title
    font-size: $fontsize-20
    font-weight: $fontweight-medium
    color: $color-black
    letter-spacing: 0.05em
    position: relative
    padding-left: 44px
    margin-bottom: 20px
    @media (max-width:$bk-sm)
      padding-left: 20px
      font-size: $fontsize-18
    &::before
      content: ""
      display: block
      width: 8px
      height: 8px
      background: $color-orange
      border-radius: 10em
      position: absolute
      top: 10px
      left: 16px
      @media (max-width:$bk-sm)
        top: 8px
        left: 0
        width: 6px
        height: 6px
  &__block
    overflow-x: auto // Enable horizontal scrolling when needed
    max-height: 576px
    width: 100%
    padding-bottom: 20px // Add padding to prevent scrollbar from overlapping content

  table
    width: 100%
    table-layout: fixed // Default to fixed layout

    thead
      tr
        background: $color-orange2
      th
        color: $color-white
        font-size: $fontsize-18
        font-weight: $fontweight-bold
        border: 3px solid #ffffff
        border-top: none

    tbody
      tr
        background: #e6e6e6
      td
        border: 3px solid #ffffff

    th, td
      padding: 14px
      text-align: center
      vertical-align: middle
      white-space: nowrap
      overflow: hidden // Default to hiding overflow
      text-overflow: ellipsis // and adding ellipsis
      &:first-child
        border-left: none

    // --- Column Width & Behavior Logic (Correctly targeting table classes) ---
    $long-content-width: 350px

    // Default widths for few columns
    &.cols-2 th, &.cols-2 td
      width: 50%
    &.cols-3 th, &.cols-3 td
      width: 33.33%
    &.cols-4 th, &.cols-4 td
      width: 25%
    &.cols-5 th, &.cols-5 td
      width: 20%

    // --- Special handling for .has-long-content (with few columns) ---
    &.has-long-content
      &.cols-2, &.cols-3, &.cols-4, &.cols-5
        table-layout: fixed // Keep it fixed to use calc()

      &.cols-2
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc(100% - #{$long-content-width})

      &.cols-3
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 2)

      &.cols-4
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 3)

      &.cols-5
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 4)

    // --- Special handling for .cols-many ---
    &.cols-many
      table-layout: auto // Use auto layout to respect content
      width: 100%      // Suggest the table fill the container
      min-width: 100%  // Force the table to be at least container width

      th, td
        min-width: 150px // Set a minimum width for readability
        overflow: visible // Show full content for scrolling
        text-overflow: clip
