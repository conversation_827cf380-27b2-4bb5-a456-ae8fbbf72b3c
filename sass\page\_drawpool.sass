// /*-- drawpool --*/
.drawpool
  $root: &
  margin-top: 34px
  @media (max-width:$bk-md)
    margin-top: 25px
  &__wrap
    display: none
    &.active
      display: block
  &__block
    display: grid // Use grid to force child to fill width
    overflow-x: auto // Enable horizontal scrolling when needed
    max-height: 325px
    width: 100%
    padding-bottom: 20px // Add padding to prevent scrollbar from overlapping content

  table
    width: 100%
    table-layout: fixed // Default to fixed layout

    thead
      tr
        background: $color-orange2
      th
        color: $color-white
        font-size: $fontsize-18
        font-weight: $fontweight-bold
        border: 3px solid #ffffff
        border-top: none

    tbody
      tr
        background: #e6e6e6
      td
        border: 3px solid #ffffff

    th, td
      padding: 14px
      text-align: center
      vertical-align: middle
      white-space: nowrap
      overflow: hidden // Default to hiding overflow
      text-overflow: ellipsis // and adding ellipsis
      &:first-child
        border-left: none

    // --- Column Width & Behavior Logic (Adjusted for smaller container) ---
    $long-content-width: 180px // Adjusted for smaller container

    // Default widths for few columns
    &.cols-2 th, &.cols-2 td
      width: 50%
    &.cols-3 th, &.cols-3 td
      width: 33.33%
    &.cols-4 th, &.cols-4 td
      width: 25%
    &.cols-5 th, &.cols-5 td
      width: 20%

    // --- Special handling for .has-long-content (with few columns) ---
    &.has-long-content
      &.cols-2, &.cols-3, &.cols-4, &.cols-5
        table-layout: fixed // Keep it fixed to use calc()

      &.cols-2
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc(100% - #{$long-content-width})

      &.cols-3
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 2)

      &.cols-4
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 3)

      &.cols-5
        th.long-content, td.long-content
          width: $long-content-width
        th:not(.long-content), td:not(.long-content)
          width: calc((100% - #{$long-content-width}) / 4)

    // --- Special handling for .cols-many ---
    &.cols-many
      table-layout: auto // Use auto layout to respect content
      width: 100%      // Suggest the table fill the container
      min-width: 100%  // Force the table to be at least container width

      th, td
        min-width: 120px // Adjusted for smaller container
        overflow: visible // Show full content, no ellipsis
        text-overflow: clip
