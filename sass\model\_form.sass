// /*----- form -----*/
.form
  $form: &
  padding-top: 116px
  @media (max-width:$bk-sm)
    padding-top: 60px
    padding-bottom: 0
  &__wrap
    display: inline-flex
    width: calc( 100% + 64px )
    margin-left: -32px
    margin-right: -32px
    @media (max-width:$bk-xl)
      width: calc( 100% + 30px )
      margin-left: -15px
      margin-right: -15px
    @media (max-width:$bk-lg)
      width: 100%
      margin-left: 0
      margin-right: 0
      flex-wrap: wrap
    &-item
      background: #ffffff
      border-radius: 30px
      box-shadow: 5px 10px 35px rgba(141, 141, 141,0.25)
      padding: 30px 44px 35px
      width: calc( 50% - 64px )
      margin-left: 32px
      margin-right: 32px
      @media (max-width:$bk-xl)
        width: calc( 50% - 30px )
        margin-left: 15px
        margin-right: 15px
      @media (max-width:$bk-lg)
        padding: 30px 15px
      @media (max-width:$bk-md)
        width: 100%
        margin-left: 0
        margin-right: 0
        margin-top: 30px
        &:first-child
          margin-top: 0
    &--colspan
      width: 100%
      margin-left: 0
      margin-right: 0
      #{$form}__wrap-item
        width: 100%
        margin-left: 0
        margin-right: 0
  &__head
    display: inline-flex
    justify-content: space-between
    width: 100%
    @media (max-width:$bk-lg)
      flex-wrap: wrap
  &__body
  &__footer
    margin-top: 50px
    display: inline-flex
    width: 100%
    justify-content: center
    @media (max-width:$bk-md)
      margin-top: 30px
    &--participants
      margin-top: 15px
      justify-content: flex-end
    .btn
      margin-left: 36px
      @media (max-width:$bk-tb)
        margin-left: 20px
      @media (max-width:$bk-sm)
        margin-left: 10px
      @media (max-width:$bk-mbxs)
        width: 124px
        margin-left: 5px
      &:first-child
        margin-left: 0
  &__btn2
    margin-top: 80px
    text-align: center
    @media (max-width:$bk-md)
      margin-top: 30px
  &__title
    font-size: $fontsize-26
    font-weight: $fontweight-bold
    position: relative
    color: $color-gray5
    letter-spacing: 0.05em
    position: relative
    padding-left: 125px
    @media (max-width:$bk-xl)
      padding-left: 80px
    @media (max-width:$bk-lg)
      padding-left: 40px
      text-align: center
      width: 100%
    @media (max-width:$bk-mblg)
      font-size: $fontsize-22
    &-icon
      position: absolute
      top: -95px
      left: -40px
      width: 162px
      pointer-events: none
      @media (max-width:$bk-xl)
        top: -65px
        width: 120px
      @media (max-width:$bk-lg)
        top: -25px
        left: calc( 50% - 105px )
        width: 70px
      @media (max-width:$bk-mblg)
        top: -20px
        left: calc( 50% - 90px )
        width: 60px
  &__btn
    display: inline-flex
    @media (max-width:$bk-lg)
      width: 100%
      justify-content: center
      margin-top: 20px
    .btn
      margin-left: 8px
      @media (max-width:$bk-mblg)
        margin-left: 4px
      &:first-child
        margin-left: 0

  //以下是表單元件樣式

  &__input
    appearance: none
    position: relative
    z-index: 1
    padding: 0 18px
    font-size: $fontsize-primary
    font-weight: $fontweight-medium
    color: $color-gray5
    letter-spacing: 0.05em
    background: $color-gray3
    border: none
    border-radius: 8px
    box-shadow: 0 0 7px rgba(0, 0, 0,0.14) inset
    height: 55px
    width: 100%
    &:disabled,[disabled]
      box-shadow: none
      background: $color-gray6
      color: $color-gray7
      cursor: not-allowed
      // for ios
      -webkit-text-fill-color: $color-gray7
      opacity: 1
    &::placeholder
      color: $color-gray4

  &__selectbox
    position: relative
    width: 100%
    border-radius: 8px
    background: $color-gray3
    &::before
      content: ""
      display: block
      width: 17px
      height: 10px
      position: absolute
      top: calc(50% - 5px)
      right: 15px
      background: url($path+'icon-arrow.svg')
      background-size: 100% auto
      background-position: center top
      background-repeat: no-repeat
  &__select
    appearance: none
    padding-top: 0
    padding-bottom: 0
    padding-right: 40px
    padding-left: 18px
    font-size: $fontsize-primary
    font-weight: $fontweight-medium
    color: $color-gray5
    background: transparent
    border: none
    border-radius: 8px
    box-shadow: 0 0 7px rgba(0, 0, 0,0.14) inset
    width: 100%
    height: 55px
    position: relative
    z-index: 1
    &::-ms-expand
      display: none
    &:disabled,[disabled]
      box-shadow: none
      background: $color-gray6
      color: $color-gray7
      cursor: not-allowed
      // for ios
      -webkit-text-fill-color: $color-gray7
      opacity: 1
    &:has(option:checked[value=""])
      color: $color-gray4
    option
      &:first-child
        color: $color-gray4
      &:not(:first-child)
        color: $color-gray5

  &__textarea
    appearance: none
    position: relative
    z-index: 1
    padding: 15px 18px
    font-size: $fontsize-primary
    font-weight: $fontweight-medium
    color: $color-gray5
    letter-spacing: 0.05em
    line-height: 1.6
    background: $color-gray3
    border: none
    border-radius: 8px
    box-shadow: 0 0 7px rgba(0, 0, 0,0.14) inset
    height: 320px
    width: 100%
    resize: none
    &:disabled,[disabled]
      box-shadow: none
      background: $color-gray6
      color: $color-gray7
      cursor: not-allowed
      // for ios
      -webkit-text-fill-color: $color-gray7
      opacity: 1
    &::placeholder
      color: $color-gray4

  &__label
    display: inline-block
    padding-right: 14px
    font-size: $fontsize-20
    font-weight: $fontweight-bold
    color: #383838
    min-width: 116px
    @media (max-width:$bk-mbxs)
      min-width: 100px

  &__radioblock
    display: inline-flex
    width: 100%
    flex-wrap: wrap
    #{$form}__radiobox
      width: 238px
      @media (max-width:$bk-tb)
        width: 200px
      @media (max-width:$bk-sm)
        width: 100%
        margin-top: 10px
        &:first-child
          margin-top: 0
  &__radiobox
    #{$form}__radio
      display: none
  &__radio
    &:disabled  + #{$form}__radiolabel,
    &[disabled]  + #{$form}__radiolabel
      cursor: not-allowed
      &::before
        background: $color-gray6
    &:disabled:checked  + #{$form}__radiolabel,
    &[disabled][checked]  + #{$form}__radiolabel
      &::after
        background: $color-gray7
    &:checked + #{$form}__radiolabel
      &::after
        content: ""
        display: block
        width: 15px
        height: 15px
        position: absolute
        left: 5px
        top: 5px
        background: $color-orange
        border-radius: 100%
  &__radiolabel
    cursor: pointer
    position: relative
    display: inline-flex
    font-size: $fontsize-18
    font-weight: $fontweight-bold
    color: $color-gray8
    text-align: left
    line-height: 1
    &::before
      content: ""
      display: block
      width: 25px
      min-width: 25px
      height: 25px
      background-color: #f4f4f4
      border: 1px solid #d4d4d4
      border-radius: 100%
      box-sizing: border-box
    span
      margin-left: 7px
    a
      color: $color-gray
      text-decoration: underline

.participants-count
  font-size: 0.875rem
  color: #666
  text-align: right
  padding: 10px 0
  span
    font-weight: bold
    color: $color-orange

.warning-message
  color: red
  font-weight: bold
  margin-bottom: 10px

.row-error
  td
    background-color: #ffe6e6 !important
